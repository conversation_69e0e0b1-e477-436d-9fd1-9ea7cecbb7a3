{"root": ["./src/constants.ts", "./src/main.tsx", "./src/routetree.gen.ts", "./src/vite-env.d.ts", "./src/components/errorboundary.tsx", "./src/components/googleaccountconnection.tsx", "./src/components/loader.tsx", "./src/components/profiledropdown.tsx", "./src/components/profileitem.tsx", "./src/components/profileoverlay.tsx", "./src/components/searchcommand.tsx", "./src/components/sidebar.tsx", "./src/components/titlebar.tsx", "./src/components/auth-provider.tsx", "./src/components/mode-toggle.tsx", "./src/components/theme-provider.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/google-sign-in-button.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/tooltip.tsx", "./src/contexts/foldercontext.tsx", "./src/contexts/profileoverlaycontext.tsx", "./src/db/auth.ts", "./src/db/draw.ts", "./src/db/supabase.ts", "./src/hooks/isauthenticated.tsx", "./src/hooks/useauth.tsx", "./src/hooks/usefolders.tsx", "./src/hooks/usegoogleconnection.tsx", "./src/hooks/usepages.tsx", "./src/lib/schemas.ts", "./src/lib/utils.ts", "./src/routes/__root.tsx", "./src/routes/_authenticated.tsx", "./src/routes/index.tsx", "./src/routes/login.lazy.tsx", "./src/routes/signup.lazy.tsx", "./src/routes/_authenticated/page.$id.lazy.tsx", "./src/routes/_authenticated/pages.lazy.tsx", "./src/stores/drawdatastore.ts", "./src/views/homepage.tsx", "./src/views/layout.tsx", "./src/views/login.tsx", "./src/views/navbar.tsx", "./src/views/nodata.tsx", "./src/views/notfound.tsx", "./src/views/page.tsx", "./src/views/pages.tsx", "./src/views/signup.tsx"], "version": "5.8.3"}